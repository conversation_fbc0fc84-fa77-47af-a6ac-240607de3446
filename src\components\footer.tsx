'use client'

import { Send } from 'lucide-react'
import { useState } from 'react'

export function Footer() {
  const [email, setEmail] = useState('')

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email)
    setEmail('')
  }

  return (
    <footer className="bg-gradient-to-r from-blue-600 to-purple-700 text-white py-12">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {/* Newsletter Section */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Subscribe to our Newsletter</h3>
            <p className="text-blue-100 mb-6 text-sm">
              Get updated about admission forms, deadlines and articles to help you through the process.
            </p>
            <form onSubmit={handleSubmit} className="flex">
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter email here..."
                className="flex-1 px-4 py-2 bg-white/10 border border-white/20 rounded-l-md text-white placeholder-blue-200 focus:outline-none focus:ring-2 focus:ring-cyan-400"
                required
              />
              <button
                type="submit"
                className="bg-cyan-400 hover:bg-cyan-500 px-4 py-2 rounded-r-md transition-colors duration-200"
              >
                <Send className="h-5 w-5 text-white" />
              </button>
            </form>
          </div>

          {/* Important Links */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Important Links</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Schools in India</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Other Schools in India</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Colleges in India</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Advertise With Us</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Common Admissions</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Edunify India</a></li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="text-xl font-semibold mb-4">Support</h3>
            <ul className="space-y-2 text-sm">
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Privacy Policy</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Terms and Conditions</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Refund Policy</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">Contact Us</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">About Us</a></li>
              <li><a href="#" className="text-blue-100 hover:text-white transition-colors">CGPA Converter</a></li>
            </ul>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="mt-12 pt-8 border-t border-white/20 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-blue-100 mb-4 md:mb-0">
            Copyright • Uniform Ventures Pvt. Ltd.
          </div>
          <div className="text-2xl font-bold">
            <span className="text-white">School</span><span className="text-cyan-400">Connect</span>
          </div>
        </div>
      </div>
    </footer>
  )
}